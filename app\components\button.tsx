import Link from "next/link";
import { IoAddCircleOutline } from "react-icons/io5";

export const CreateButton = () => {
    return (
        <Link href="/contact/create">
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-sm">
                <IoAddCircleOutline className="inline-block mr-2" size={25} />
                Create
            </button>
        </Link>
    );
};

export const EditButton = () => {
    return (
        <Link href="/contact/create">
            <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-sm">
                Edit
            </button>
        </Link>
    );
};
