import { Contact } from "@/app/generated/prisma";
import { getContacts } from "@/lib/data";

const ContactTable = async () => {
    const contacts = await getContacts();
    return (
        <table className="w-full text-sm text-left text-gray-500">
            <thead className="text-sm text-gray-700 uppercase bg-gray-50">
                <tr>
                    <th className="px-6 py-3">#</th>
                    <th className="px-6 py-3">Name</th>
                    <th className="px-6 py-3">Phone Number</th>
                    <th className="px-6 py-3">Created At</th>
                    <th className="px-6 py-3 text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                {contacts.map((contact: Contact, index: number) => (
                    <tr key={contact.id}>
                        <td className="px-6 py-3">{index + 1}</td>
                        <td className="px-6 py-3">{contact.name}</td>
                        <td className="px-6 py-3">{contact.phone}</td>
                        <td className="px-6 py-3">
                            {contact.createdAt.toLocaleDateString()}
                        </td>
                        <td className="px-6 py-3 text-center">Edit</td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
};

export default ContactTable;
